#!/usr/bin/env python3
"""
Installation script for CS2 HLTV Betting Analysis System
Installs all required dependencies with latest versions
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description=""):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"{'='*60}")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✅ Success!")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print(f"Stdout: {e.stdout}")
        if e.stderr:
            print(f"Stderr: {e.stderr}")
        return False

def install_core_packages():
    """Install core packages first"""
    core_packages = [
        "pip>=24.0",
        "setuptools>=70.0",
        "wheel>=0.42.0",
    ]
    
    for package in core_packages:
        if not run_command(f"pip install --upgrade {package}", f"Installing {package}"):
            print(f"⚠️  Warning: Failed to install {package}")

def install_main_packages():
    """Install main packages from requirements.txt"""
    
    # Core packages that should install first
    priority_packages = [
        "playwright==1.52.0",
        "beautifulsoup4==4.13.4", 
        "requests==2.32.3",
        "pandas==2.2.3",
        "numpy==2.2.6",
        "pydantic==2.11.5",
        "loguru==0.7.3"
    ]
    
    print("\n🚀 Installing priority packages...")
    for package in priority_packages:
        run_command(f"pip install {package}", f"Installing {package}")
    
    # Install playwright browsers
    print("\n🌐 Installing Playwright browsers...")
    run_command("playwright install chromium", "Installing Chromium browser for Playwright")
    
    # Install remaining packages
    print("\n📦 Installing remaining packages from requirements.txt...")
    if os.path.exists("requirements.txt"):
        run_command("pip install -r requirements.txt", "Installing from requirements.txt")
    else:
        print("❌ requirements.txt not found!")

def install_optional_packages():
    """Install optional advanced packages"""
    optional_packages = [
        "nodriver==0.46.1",
        "cloudscraper==1.2.71", 
        "playwright-stealth==1.0.6",
        "curl-cffi==0.7.4",
        "pytest==8.3.4",
        "pytest-playwright==0.6.2",
        "pytest-asyncio==0.25.0"
    ]
    
    print("\n🔧 Installing optional advanced packages...")
    for package in optional_packages:
        if not run_command(f"pip install {package}", f"Installing optional {package}"):
            print(f"⚠️  Skipping optional package: {package}")

def verify_installation():
    """Verify that key packages are installed correctly"""
    print("\n🔍 Verifying installation...")
    
    test_imports = [
        ("playwright", "Playwright"),
        ("beautifulsoup4", "BeautifulSoup"),
        ("requests", "Requests"),
        ("pandas", "Pandas"),
        ("numpy", "NumPy"),
        ("pydantic", "Pydantic"),
        ("loguru", "Loguru")
    ]
    
    failed_imports = []
    
    for package, name in test_imports:
        try:
            if package == "beautifulsoup4":
                import bs4
            else:
                __import__(package)
            print(f"✅ {name} imported successfully")
        except ImportError as e:
            print(f"❌ {name} import failed: {e}")
            failed_imports.append(name)
    
    # Test optional imports
    optional_imports = [
        ("nodriver", "NoDriver"),
        ("cloudscraper", "CloudScraper")
    ]
    
    for package, name in optional_imports:
        try:
            __import__(package)
            print(f"✅ {name} (optional) imported successfully")
        except ImportError:
            print(f"⚠️  {name} (optional) not available - will use fallback methods")
    
    if failed_imports:
        print(f"\n❌ Installation incomplete. Failed packages: {', '.join(failed_imports)}")
        return False
    else:
        print(f"\n✅ All core packages installed successfully!")
        return True

def create_test_script():
    """Create a simple test script"""
    test_script = '''#!/usr/bin/env python3
"""
Quick test script for CS2 HLTV Betting Analysis System
"""

import asyncio
import sys

async def test_basic_functionality():
    """Test basic functionality"""
    print("🧪 Testing CS2 HLTV Betting Analysis System...")
    
    try:
        # Test imports
        from main import CS2BettingAnalysisSystem
        from hltv_scraper import HLTVScraper
        from analysis_engine import CS2BettingAnalyzer
        print("✅ All core modules imported successfully")
        
        # Test analyzer
        analyzer = CS2BettingAnalyzer()
        print("✅ Analysis engine initialized")
        
        # Test system
        system = CS2BettingAnalysisSystem()
        print("✅ Main system initialized")
        
        print("\\n🎉 Basic functionality test PASSED!")
        print("\\n🚀 Ready to analyze CS2 matches!")
        print("\\nRun: python main.py")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("\\n🔧 Try running: python install_dependencies.py")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(test_basic_functionality())
'''
    
    with open("test_installation.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    print("✅ Created test_installation.py")

def main():
    """Main installation process"""
    print("🎯 CS2 HLTV Betting Analysis System - Dependency Installer")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} detected")
    
    # Install packages
    install_core_packages()
    install_main_packages()
    install_optional_packages()
    
    # Verify installation
    if verify_installation():
        create_test_script()
        
        print("\n" + "="*60)
        print("🎉 INSTALLATION COMPLETE!")
        print("="*60)
        print("\n📋 Next steps:")
        print("1. Test installation: python test_installation.py")
        print("2. Run the system: python main.py")
        print("3. Check README.md for usage instructions")
        print("\n🔗 Example HLTV URL:")
        print("https://www.hltv.org/matches/2382724/cybershoke-vs-nexus-galaxy-battle-2025-phase-2")
        
    else:
        print("\n❌ Installation had issues. Please check the errors above.")
        print("💡 Try running individual pip install commands manually.")

if __name__ == "__main__":
    main()
