# CS2 HLTV Betting Analysis System

A comprehensive Python-based web scraping and analysis system for Counter-Strike 2 match predictions using HLTV.org data. This system implements advanced anti-detection measures to bypass Cloudflare protection and provides detailed betting analysis following a rigorous statistical framework.

## Features

### 🔧 Technical Capabilities
- **Advanced Web Scraping**: Playwright-based scraping with Cloudflare bypass
- **Anti-Detection**: Stealth mode, user agent rotation, human-like behavior simulation
- **Robust Error Handling**: Retry logic, timeout management, graceful degradation
- **Modern Dependencies**: Latest versions of all packages for security and performance

### 📊 Analysis Framework
- **Multi-Source Data Collection**: HLTV.org primary, Liquipedia/Bo3.gg fallback
- **Comprehensive Risk Assessment**: Ultra-Low, Low, Medium, High risk categorization
- **Advanced Statistical Analysis**: Weighted performance metrics, confidence intervals
- **Multiple Bet Types**: Moneyline, Map Handicap, Total Maps, and more
- **Expected Value Calculations**: Kelly Criterion for optimal stake sizing

### 🎯 Prediction Methodology
- **Recent Form Analysis** (30% weight): Win/loss with recency weighting
- **Head-to-Head Dynamics** (20% weight): Historical matchup results
- **Map Pool Analysis** (25% weight): Individual map win rates and advantages
- **Player Performance** (15% weight): Rating 2.0 trends, role-specific analysis
- **Tactical Factors** (10% weight): Meta adaptation, coaching stability

## Installation

### Prerequisites
- Python 3.8 or higher
- Windows/macOS/Linux
- Internet connection for web scraping

### Step 1: Clone Repository
```bash
git clone <repository-url>
cd ollama-web
```

### Step 2: Install Dependencies
```bash
# Install Python packages
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium
```

### Step 3: Verify Installation
```bash
python -c "import playwright; print('Playwright installed successfully')"
```

## Usage

### Basic Usage
```bash
python main.py
```

The script will prompt you for an HLTV match URL. You can use the example URL or paste your own:
```
https://www.hltv.org/matches/2382724/cybershoke-vs-nexus-galaxy-battle-2025-phase-2
```

### Advanced Usage

#### Analyze Single Match
```python
import asyncio
from main import CS2BettingAnalysisSystem

async def analyze_match():
    system = CS2BettingAnalysisSystem()
    result = await system.analyze_match_from_url("HLTV_MATCH_URL")
    if result:
        print(system.format_analysis_output(result))

asyncio.run(analyze_match())
```

#### Analyze Multiple Matches
```python
import asyncio
from main import CS2BettingAnalysisSystem

async def analyze_multiple():
    system = CS2BettingAnalysisSystem()
    urls = [
        "https://www.hltv.org/matches/...",
        "https://www.hltv.org/matches/...",
    ]
    results = await system.analyze_multiple_matches(urls)
    summary = system.generate_executive_summary(results)
    print(summary)

asyncio.run(analyze_multiple())
```

## Configuration

### Scraping Settings
Edit `config.py` to customize scraping behavior:

```python
@dataclass
class ScrapingConfig:
    MIN_DELAY: float = 2.0          # Minimum delay between requests
    MAX_DELAY: float = 5.0          # Maximum delay between requests
    HEADLESS: bool = True           # Run browser in headless mode
    REQUEST_TIMEOUT: int = 30       # Request timeout in seconds
```

### Analysis Parameters
Adjust risk thresholds and weights:

```python
@dataclass
class AnalysisConfig:
    ULTRA_LOW_RISK_RANKING_GAP: int = 15    # Minimum ranking gap for ultra-low risk
    ULTRA_LOW_RISK_WIN_RATE: float = 0.75   # Minimum win rate for ultra-low risk
    MIN_EV_LOW_RISK: float = 0.10           # Minimum expected value for low risk
```

## Output Format

The system generates detailed analysis reports following the framework specification:

```
# CS2 BETTING ANALYSIS REPORT

## MATCH OVERVIEW
**Team A vs Team B**
Tournament: Tournament Name
Format: BO3
Venue: LAN
Time: 2025-01-XX XX:XX UTC

## RECOMMENDATION SUMMARY
Risk Level: 🛡️ Ultra-Low
Confidence: 82.5%
Expected Value: +15.2%

## PRIMARY BET RECOMMENDATION
• Bet Type: Moneyline
• Selection: Team A
• Recommended Stake: 3.2% of bankroll

## STATISTICAL FOUNDATION
• Ranking Gap: 12 positions
• Team A Recent Form: 8-2 L10
• Team B Recent Form: 5-5 L10
...
```

## Risk Assessment Framework

### 🛡️ Ultra-Low Risk (75-85% Confidence)
- HLTV ranking gap ≥15 positions OR Elo gap ≥200 points
- Favored team win rate ≥75% in last 10 matches
- Stable roster (60+ days), no stand-ins
- BO3/BO5 format only
- Map pool advantage ≥70% on 2+ maps

### ⚖️ Low Risk (65-75% Confidence)
- HLTV ranking gap ≥8 positions OR Elo gap ≥150 points
- Favored team win rate ≥65% in last 10 matches
- Stable roster (30+ days), max 1 experienced stand-in
- BO3/BO5 preferred

### ⚠️ Medium Risk (55-65% Confidence)
- HLTV ranking gap ≥5 positions OR demonstrated form advantage
- Recent win rate ≥55% over last 8 matches
- Reasonable roster stability (14+ days)
- Positive expected value ≥15%

### 🚫 High Risk (Automatic Exclusion)
- Insufficient data or conflicting sources
- Major roster changes in past 14 days
- BO1 matches (unless exceptional edge >25% EV)
- Match-fixing concerns or integrity issues

## Troubleshooting

### Common Issues

#### 1. Cloudflare 403 Errors
```
Error: 403 Forbidden received
```
**Solution**: The system includes automatic Cloudflare handling. If persistent:
- Increase delays in `config.py`
- Try running with `HEADLESS = False` for debugging
- Check if your IP is rate-limited

#### 2. Browser Installation Issues
```
Error: Playwright browser not found
```
**Solution**:
```bash
playwright install chromium
# or
python -m playwright install chromium
```

#### 3. Missing Dependencies
```
ModuleNotFoundError: No module named 'playwright'
```
**Solution**:
```bash
pip install -r requirements.txt
```

### Debug Mode
Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Data Sources

### Primary Sources (Tier 1)
- **HLTV.org**: Match data, team rankings, player statistics
- **HLTV Rankings**: Current team rankings and Elo ratings
- **HLTV Statistics**: Historical performance data

### Fallback Sources (Tier 2)
- **Liquipedia**: Roster changes, tournament brackets
- **Bo3.gg**: Recent form, match predictions
- **Dust2.in**: Betting markets, odds comparison

## Legal and Ethical Considerations

### Responsible Usage
- **Respect Rate Limits**: Built-in delays prevent server overload
- **Educational Purpose**: This tool is for analysis and learning
- **Gambling Awareness**: Only bet what you can afford to lose
- **Age Restrictions**: Must be 18+ and in legal jurisdiction

### Terms of Service
- Complies with HLTV.org robots.txt
- Non-commercial use only
- No redistribution of scraped data
- Users responsible for compliance with local laws

## Contributing

### Development Setup
```bash
# Install development dependencies
pip install -r requirements.txt
pip install pytest black flake8

# Run tests
python -m pytest test_scraper.py

# Format code
black *.py

# Lint code
flake8 *.py
```

### Reporting Issues
Please include:
- Python version
- Operating system
- Full error traceback
- HLTV URL being analyzed
- Configuration settings

## License

This project is for educational and research purposes only. Users are responsible for compliance with applicable laws and website terms of service.

## Disclaimer

This software is provided "as is" without warranty. The authors are not responsible for any financial losses resulting from the use of this software. Always gamble responsibly and within your means.
