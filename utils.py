"""
Utility functions for HLTV scraping and data processing
"""

import re
import time
import random
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Tuple
from urllib.parse import urljoin, urlparse
import logging

from config import SCRAPING_CONFIG, CS2_MAPS
from data_models import ValidationResult, ScrapingResult

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def random_delay(min_delay: float = None, max_delay: float = None) -> None:
    """Add random delay to avoid detection"""
    min_delay = min_delay or SCRAPING_CONFIG.MIN_DELAY
    max_delay = max_delay or SCRAPING_CONFIG.MAX_DELAY
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)

def get_random_user_agent() -> str:
    """Get random user agent from config"""
    return random.choice(SCRAPING_CONFIG.USER_AGENTS)

def clean_text(text: str) -> str:
    """Clean and normalize text data"""
    if not text:
        return ""
    
    # Remove extra whitespace and newlines
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove special characters that might cause issues
    text = re.sub(r'[^\w\s\-\.\%\(\)\/\:]', '', text)
    
    return text

def parse_percentage(text: str) -> float:
    """Parse percentage string to float"""
    if not text:
        return 0.0
    
    # Extract percentage value
    match = re.search(r'(\d+(?:\.\d+)?)%?', text)
    if match:
        return float(match.group(1)) / 100.0 if '%' in text else float(match.group(1))
    
    return 0.0

def parse_rating(text: str) -> float:
    """Parse rating string to float"""
    if not text:
        return 0.0
    
    # Extract rating value
    match = re.search(r'(\d+\.\d+)', text)
    if match:
        return float(match.group(1))
    
    return 0.0

def parse_ranking(text: str) -> int:
    """Parse ranking string to integer"""
    if not text:
        return 0
    
    # Extract ranking number
    match = re.search(r'#?(\d+)', text)
    if match:
        return int(match.group(1))
    
    return 0

def normalize_team_name(name: str) -> str:
    """Normalize team name for consistent matching"""
    if not name:
        return ""
    
    # Convert to lowercase and remove special characters
    normalized = re.sub(r'[^\w\s]', '', name.lower())
    normalized = re.sub(r'\s+', '_', normalized.strip())
    
    return normalized

def normalize_map_name(map_name: str) -> str:
    """Normalize map name to standard format"""
    if not map_name:
        return ""
    
    map_name = map_name.lower().strip()
    
    # Handle common variations
    map_variations = {
        'de_mirage': 'mirage',
        'de_inferno': 'inferno',
        'de_dust2': 'dust2',
        'de_nuke': 'nuke',
        'de_overpass': 'overpass',
        'de_vertigo': 'vertigo',
        'de_ancient': 'ancient',
        'de_anubis': 'anubis'
    }
    
    return map_variations.get(map_name, map_name)

def validate_map_name(map_name: str) -> bool:
    """Check if map name is valid CS2 map"""
    normalized = normalize_map_name(map_name)
    return normalized in CS2_MAPS

def parse_match_time(time_str: str) -> Optional[datetime]:
    """Parse match time string to datetime object"""
    if not time_str:
        return None
    
    try:
        # Common HLTV time formats
        formats = [
            "%Y-%m-%d %H:%M",
            "%d/%m/%Y %H:%M",
            "%m/%d/%Y %H:%M",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%dT%H:%M:%SZ"
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
                
        # If no format matches, try to extract date/time components
        # This is a fallback for unusual formats
        logger.warning(f"Could not parse time string: {time_str}")
        return None
        
    except Exception as e:
        logger.error(f"Error parsing time string '{time_str}': {e}")
        return None

def calculate_win_rate(wins: int, losses: int) -> float:
    """Calculate win rate from wins and losses"""
    total = wins + losses
    if total == 0:
        return 0.0
    return wins / total

def calculate_confidence_interval(success_rate: float, sample_size: int, confidence_level: float = 0.95) -> Tuple[float, float]:
    """Calculate confidence interval for success rate"""
    if sample_size == 0:
        return 0.0, 0.0
    
    import math
    
    # Z-score for confidence level
    z_scores = {0.90: 1.645, 0.95: 1.96, 0.99: 2.576}
    z = z_scores.get(confidence_level, 1.96)
    
    # Standard error
    se = math.sqrt((success_rate * (1 - success_rate)) / sample_size)
    
    # Margin of error
    margin = z * se
    
    lower = max(0.0, success_rate - margin)
    upper = min(1.0, success_rate + margin)
    
    return lower, upper

def validate_data_freshness(timestamp: datetime, max_age_hours: int = 24) -> bool:
    """Check if data is fresh enough for analysis"""
    if not timestamp:
        return False
    
    age = datetime.now() - timestamp
    return age.total_seconds() / 3600 <= max_age_hours

def generate_match_id(team_a: str, team_b: str, tournament: str, date: datetime) -> str:
    """Generate unique match ID"""
    data = f"{team_a}_{team_b}_{tournament}_{date.strftime('%Y%m%d')}"
    return hashlib.md5(data.encode()).hexdigest()[:12]

def extract_match_id_from_url(url: str) -> Optional[str]:
    """Extract match ID from HLTV URL"""
    if not url:
        return None
    
    # HLTV match URLs typically have format: /matches/ID/team1-vs-team2-tournament
    match = re.search(r'/matches/(\d+)/', url)
    if match:
        return match.group(1)
    
    return None

def is_valid_hltv_url(url: str) -> bool:
    """Check if URL is a valid HLTV URL"""
    if not url:
        return False
    
    parsed = urlparse(url)
    return parsed.netloc == 'www.hltv.org'

def build_hltv_url(path: str, base_url: str = "https://www.hltv.org") -> str:
    """Build complete HLTV URL from path"""
    return urljoin(base_url, path)

def extract_numbers_from_text(text: str) -> List[float]:
    """Extract all numbers from text"""
    if not text:
        return []
    
    numbers = re.findall(r'\d+(?:\.\d+)?', text)
    return [float(num) for num in numbers]

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers, return default if division by zero"""
    if denominator == 0:
        return default
    return numerator / denominator

def weighted_average(values: List[float], weights: List[float]) -> float:
    """Calculate weighted average"""
    if not values or not weights or len(values) != len(weights):
        return 0.0
    
    if sum(weights) == 0:
        return 0.0
    
    return sum(v * w for v, w in zip(values, weights)) / sum(weights)

def validate_scraping_result(result: ScrapingResult) -> ValidationResult:
    """Validate scraping result quality"""
    if not result.success:
        return ValidationResult(
            is_valid=False,
            confidence_score=0.0,
            missing_data=["Scraping failed"],
            data_age_hours=float('inf')
        )
    
    # Calculate data age
    age_hours = (datetime.now() - result.timestamp).total_seconds() / 3600
    
    # Basic validation
    is_valid = result.data is not None
    confidence_score = 1.0 if is_valid else 0.0
    
    # Adjust confidence based on response time (slower = less reliable)
    if result.response_time > 10:
        confidence_score *= 0.8
    elif result.response_time > 5:
        confidence_score *= 0.9
    
    # Adjust confidence based on data age
    if age_hours > 24:
        confidence_score *= 0.7
    elif age_hours > 12:
        confidence_score *= 0.9
    
    return ValidationResult(
        is_valid=is_valid,
        confidence_score=confidence_score,
        data_age_hours=age_hours
    )
