"""
Test suite for HLTV scraper functionality
"""

import asyncio
import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from hltv_scraper import HLTVS<PERSON>raper
from analysis_engine import CS2BettingAnalyzer
from data_models import Team, MatchInfo, HeadToHead, MatchFormat, RiskLevel
from config import URL_CONFIG

class TestHLTVScraper:
    """Test cases for HLTV scraper"""
    
    @pytest.mark.asyncio
    async def test_browser_initialization(self):
        """Test browser initialization and cleanup"""
        async with H<PERSON>VScraper() as scraper:
            assert scraper.browser is not None
            assert scraper.context is not None
            assert scraper.page is not None
            
    @pytest.mark.asyncio
    async def test_cloudflare_detection(self):
        """Test Cloudflare challenge detection"""
        scraper = HLTVScraper()
        await scraper.initialize_browser()
        
        # Mock page content with Cloudflare indicators
        mock_content = """
        <html>
            <body>
                <div>Checking your browser before accessing</div>
                <div>DDoS protection by Cloudflare</div>
            </body>
        </html>
        """
        
        with patch.object(scraper.page, 'content', return_value=mock_content):
            is_challenge = await scraper.is_cloudflare_challenge()
            assert is_challenge is True
            
        await scraper.close()
        
    @pytest.mark.asyncio
    async def test_navigation_retry(self):
        """Test navigation with retry logic"""
        scraper = HLTVScraper()
        await scraper.initialize_browser()
        
        # Test with invalid URL (should fail gracefully)
        result = await scraper.navigate_with_retry("https://invalid-url-test.com", max_retries=1)
        assert result is False
        
        await scraper.close()
        
    def test_match_info_parsing(self):
        """Test match info data structure"""
        match_info = MatchInfo(
            match_id="test123",
            team_a="Team A",
            team_b="Team B",
            tournament="Test Tournament",
            match_format=MatchFormat.BO3,
            scheduled_time=datetime.now(),
            venue="LAN"
        )
        
        assert match_info.team_a == "Team A"
        assert match_info.team_b == "Team B"
        assert match_info.match_format == MatchFormat.BO3
        assert match_info.venue == "LAN"

class TestCS2BettingAnalyzer:
    """Test cases for betting analysis engine"""
    
    def setup_method(self):
        """Set up test data"""
        self.analyzer = CS2BettingAnalyzer()
        
        # Create test teams
        self.team_a = Team(
            name="Team A",
            ranking=5,
            elo_rating=1200,
            recent_form=[True, True, False, True, True],  # 4-1 record
            avg_team_rating=1.15,
            opening_duel_success_rate=0.65,
            trade_kill_efficiency=0.80
        )
        
        self.team_b = Team(
            name="Team B", 
            ranking=15,
            elo_rating=1000,
            recent_form=[False, True, False, False, True],  # 2-3 record
            avg_team_rating=1.05,
            opening_duel_success_rate=0.55,
            trade_kill_efficiency=0.70
        )
        
        # Create test match info
        self.match_info = MatchInfo(
            match_id="test123",
            team_a="Team A",
            team_b="Team B",
            tournament="Test Tournament",
            match_format=MatchFormat.BO3,
            scheduled_time=datetime.now(),
            venue="LAN"
        )
        
        # Create test head-to-head
        self.head_to_head = HeadToHead(
            team_a_wins=3,
            team_b_wins=1,
            total_matches=4
        )
        
    def test_data_quality_assessment(self):
        """Test data quality assessment"""
        quality = self.analyzer._assess_data_quality(self.team_a, self.team_b, self.head_to_head)
        assert 0.0 <= quality <= 1.0
        assert quality > 0.5  # Should be decent quality with our test data
        
    def test_performance_score_calculation(self):
        """Test performance score calculation"""
        scores = self.analyzer._calculate_performance_scores(self.team_a, self.team_b)
        
        assert 'team_a' in scores
        assert 'team_b' in scores
        assert 'total' in scores['team_a']
        assert 'total' in scores['team_b']
        
        # Team A should have higher score (better ranking, form, etc.)
        assert scores['team_a']['total'] > scores['team_b']['total']
        
    def test_recent_form_calculation(self):
        """Test recent form score calculation"""
        score_a = self.analyzer._calculate_recent_form_score(self.team_a)
        score_b = self.analyzer._calculate_recent_form_score(self.team_b)
        
        assert 0.0 <= score_a <= 1.0
        assert 0.0 <= score_b <= 1.0
        assert score_a > score_b  # Team A has better recent form
        
    def test_ranking_score_calculation(self):
        """Test ranking-based score calculation"""
        score_a = self.analyzer._calculate_ranking_score(self.team_a)
        score_b = self.analyzer._calculate_ranking_score(self.team_b)
        
        assert 0.0 <= score_a <= 1.0
        assert 0.0 <= score_b <= 1.0
        assert score_a > score_b  # Team A has better ranking (5 vs 15)
        
    def test_risk_level_assessment(self):
        """Test risk level assessment"""
        performance_scores = self.analyzer._calculate_performance_scores(self.team_a, self.team_b)
        risk_level = self.analyzer._assess_risk_level(
            self.team_a, self.team_b, self.match_info, performance_scores
        )
        
        assert isinstance(risk_level, RiskLevel)
        # With ranking gap of 10 and good form, should be Low or Ultra-Low risk
        assert risk_level in [RiskLevel.ULTRA_LOW, RiskLevel.LOW]
        
    def test_win_probability_calculation(self):
        """Test win probability calculation"""
        performance_scores = self.analyzer._calculate_performance_scores(self.team_a, self.team_b)
        map_pool_analysis = self.analyzer._analyze_map_pool(self.team_a, self.team_b)
        player_analysis = self.analyzer._analyze_player_matchups(self.team_a, self.team_b)
        h2h_analysis = self.analyzer._analyze_head_to_head(self.head_to_head)
        
        win_prob = self.analyzer._calculate_win_probability(
            performance_scores, map_pool_analysis, player_analysis, h2h_analysis
        )
        
        assert 0.05 <= win_prob <= 0.95
        assert win_prob > 0.5  # Team A should be favored
        
    def test_confidence_calculation(self):
        """Test confidence calculation"""
        performance_scores = self.analyzer._calculate_performance_scores(self.team_a, self.team_b)
        risk_level = RiskLevel.LOW
        data_quality = 0.8
        
        confidence = self.analyzer._calculate_confidence(risk_level, data_quality, performance_scores)
        
        assert 0.0 <= confidence <= 1.0
        assert confidence >= 0.5  # Should have reasonable confidence
        
    def test_kelly_stake_calculation(self):
        """Test Kelly Criterion stake calculation"""
        win_prob = 0.6
        odds = 2.0
        
        stake = self.analyzer._calculate_kelly_stake(win_prob, odds)
        
        assert 0.0 <= stake <= 0.05  # Max 5% stake
        assert stake > 0.0  # Should recommend some stake for positive EV
        
    def test_full_analysis(self):
        """Test complete analysis workflow"""
        result = self.analyzer.analyze_match(
            self.match_info,
            self.team_a,
            self.team_b,
            self.head_to_head
        )
        
        assert result is not None
        assert result.risk_level in [RiskLevel.ULTRA_LOW, RiskLevel.LOW, RiskLevel.MEDIUM, RiskLevel.HIGH]
        assert 0.0 <= result.confidence <= 1.0
        assert result.recommended_bet_type in ['moneyline', 'map_handicap', 'total_maps']
        assert result.recommended_selection in [self.team_a.name, self.team_b.name]
        assert 0.0 <= result.recommended_stake_percentage <= 0.05

class TestUtilityFunctions:
    """Test utility functions"""
    
    def test_text_cleaning(self):
        """Test text cleaning function"""
        from utils import clean_text
        
        dirty_text = "  Team Name\n\t  "
        clean = clean_text(dirty_text)
        assert clean == "Team Name"
        
    def test_percentage_parsing(self):
        """Test percentage parsing"""
        from utils import parse_percentage
        
        assert parse_percentage("75%") == 0.75
        assert parse_percentage("75.5%") == 0.755
        assert parse_percentage("75") == 75.0  # Without % symbol
        assert parse_percentage("") == 0.0
        
    def test_rating_parsing(self):
        """Test rating parsing"""
        from utils import parse_rating
        
        assert parse_rating("1.25") == 1.25
        assert parse_rating("Rating: 1.15") == 1.15
        assert parse_rating("") == 0.0
        
    def test_ranking_parsing(self):
        """Test ranking parsing"""
        from utils import parse_ranking
        
        assert parse_ranking("#5") == 5
        assert parse_ranking("5") == 5
        assert parse_ranking("Rank 10") == 10
        assert parse_ranking("") == 0

# Integration test
@pytest.mark.asyncio
async def test_integration_workflow():
    """Test the complete workflow with mock data"""
    from main import CS2BettingAnalysisSystem
    
    system = CS2BettingAnalysisSystem()
    
    # This would normally scrape real data, but we'll test the structure
    # In a real test, you'd mock the scraper responses
    assert system.analyzer is not None
    
    # Test output formatting with mock analysis result
    mock_analysis = CS2BettingAnalyzer().analyze_match(
        MatchInfo(
            match_id="test",
            team_a="Team A",
            team_b="Team B", 
            tournament="Test",
            match_format=MatchFormat.BO3,
            scheduled_time=datetime.now(),
            venue="LAN"
        ),
        Team(name="Team A", ranking=5),
        Team(name="Team B", ranking=15),
        HeadToHead()
    )
    
    formatted = system.format_analysis_output(mock_analysis)
    assert "CS2 BETTING ANALYSIS REPORT" in formatted
    assert "Team A vs Team B" in formatted

if __name__ == "__main__":
    # Run basic tests
    print("Running CS2 Betting Analysis Tests...")
    
    # Test data models
    team = Team(name="Test Team", ranking=10)
    assert team.name == "Test Team"
    assert team.ranking == 10
    print("✓ Data models test passed")
    
    # Test analyzer initialization
    analyzer = CS2BettingAnalyzer()
    assert analyzer is not None
    print("✓ Analyzer initialization test passed")
    
    print("Basic tests completed successfully!")
    print("Run 'pytest test_scraper.py' for full test suite")
