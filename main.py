"""
Main execution script for CS2 HLTV betting analysis
"""

import asyncio
import sys
from datetime import datetime
from typing import List, Optional
import logging

from enhanced_scraper import EnhancedHLTVScraper
from analysis_engine import CS2BettingAnalyzer
from data_models import (
    MatchInfo, Team, HeadToHead, AnalysisResult,
    RiskLevel, MatchFormat
)
from config import RISK_LEVELS
from utils import generate_match_id

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cs2_betting_analysis.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class CS2BettingAnalysisSystem:
    """Main system for CS2 betting analysis"""

    def __init__(self):
        self.analyzer = CS2BettingAnalyzer()

    async def analyze_match_from_url(self, hltv_url: str) -> Optional[AnalysisResult]:
        """Analyze a match from HLTV URL"""
        try:
            logger.info(f"Starting analysis for match: {hltv_url}")

            async with EnhancedHLTVScraper() as scraper:
                # Step 1: Scrape basic match information
                match_result = await scraper.scrape_match_info(hltv_url)
                if not match_result.success:
                    logger.error(f"Failed to scrape match info: {match_result.error_message}")
                    return None

                match_info = match_result.data
                if not isinstance(match_info, MatchInfo):
                    logger.error("Invalid match info data type")
                    return None

                logger.info(f"Match: {match_info.team_a} vs {match_info.team_b}")

                # Step 2: Scrape team data
                team_a_result = await scraper.scrape_team_ranking(match_info.team_a)
                team_b_result = await scraper.scrape_team_ranking(match_info.team_b)

                if not team_a_result.success or not team_b_result.success:
                    logger.warning("Failed to get complete team ranking data")

                team_a = team_a_result.data if team_a_result.success and isinstance(team_a_result.data, Team) else Team(name=match_info.team_a)
                team_b = team_b_result.data if team_b_result.success and isinstance(team_b_result.data, Team) else Team(name=match_info.team_b)

                # Step 3: Create placeholder head-to-head data
                # In a full implementation, this would be scraped from HLTV
                head_to_head = HeadToHead()

                # Step 4: Perform analysis
                analysis_result = self.analyzer.analyze_match(
                    match_info=match_info,
                    team_a=team_a,
                    team_b=team_b,
                    head_to_head=head_to_head
                )

                logger.info(f"Analysis completed: {analysis_result.risk_level.value} risk")
                return analysis_result

        except Exception as e:
            logger.error(f"Error analyzing match: {e}")
            return None

    def format_analysis_output(self, analysis: AnalysisResult) -> str:
        """Format analysis result according to framework specification"""

        # Get risk level emoji
        risk_emoji = RISK_LEVELS[analysis.risk_level.value]

        output = f"""
# CS2 BETTING ANALYSIS REPORT

## MATCH OVERVIEW
**{analysis.match_info.team_a} vs {analysis.match_info.team_b}**
Tournament: {analysis.match_info.tournament}
Format: {analysis.match_info.match_format.value}
Venue: {analysis.match_info.venue}
Time: {analysis.match_info.scheduled_time.strftime('%Y-%m-%d %H:%M UTC')}

## RECOMMENDATION SUMMARY
Risk Level: {risk_emoji} {analysis.risk_level.value.title()}
Confidence: {analysis.confidence:.1%}
Expected Value: {analysis.expected_value:+.1%}

## PRIMARY BET RECOMMENDATION
• Bet Type: {analysis.recommended_bet_type.title()}
• Selection: {analysis.recommended_selection}
• Recommended Stake: {analysis.recommended_stake_percentage:.1%} of bankroll

## STATISTICAL FOUNDATION
"""

        # Add statistical foundation
        for key, value in analysis.statistical_foundation.items():
            output += f"• {key.replace('_', ' ').title()}: {value}\n"

        output += "\n## ADVANCED METRICS\n"

        # Add advanced metrics
        for key, value in analysis.advanced_metrics.items():
            if isinstance(value, float):
                output += f"• {key.replace('_', ' ').title()}: {value:.3f}\n"
            else:
                output += f"• {key.replace('_', ' ').title()}: {value}\n"

        output += "\n## TACTICAL ANALYSIS\n"

        # Add tactical analysis
        for key, value in analysis.tactical_analysis.items():
            output += f"• {key.replace('_', ' ').title()}: {value}\n"

        # Add risk factors if any
        if analysis.risk_factors:
            output += "\n## RISK FACTORS\n"
            for factor in analysis.risk_factors:
                output += f"• {factor}\n"

        # Add alternative bets if any
        if analysis.alternative_bets:
            output += "\n## ALTERNATIVE BET OPTIONS\n"
            for i, bet in enumerate(analysis.alternative_bets, 1):
                bet_type = str(bet.get('bet_type', '')).title()
                output += f"{i}. {bet_type}: {bet.get('selection', '')}\n"
                output += f"   Rationale: {bet.get('rationale', '')}\n"

        output += f"\n## DATA QUALITY\n"
        output += f"• Data Quality Score: {analysis.data_quality_score:.1%}\n"
        output += f"• Sources Verified: {analysis.sources_verified}\n"
        output += f"• Last Updated: {analysis.last_updated.strftime('%Y-%m-%d %H:%M UTC')}\n"

        return output

    async def analyze_multiple_matches(self, match_urls: List[str]) -> List[AnalysisResult]:
        """Analyze multiple matches and return sorted by risk/confidence"""
        results = []

        for url in match_urls:
            result = await self.analyze_match_from_url(url)
            if result:
                results.append(result)

        # Sort by risk level (lower risk first) and then by confidence (higher first)
        risk_order = {
            RiskLevel.ULTRA_LOW: 0,
            RiskLevel.LOW: 1,
            RiskLevel.MEDIUM: 2,
            RiskLevel.HIGH: 3
        }

        results.sort(key=lambda x: (risk_order[x.risk_level], -x.confidence))
        return results

    def generate_executive_summary(self, analyses: List[AnalysisResult]) -> str:
        """Generate executive summary of all analyses"""

        if not analyses:
            return "No valid analyses to summarize."

        # Filter by risk level
        ultra_low = [a for a in analyses if a.risk_level == RiskLevel.ULTRA_LOW]
        low = [a for a in analyses if a.risk_level == RiskLevel.LOW]
        medium = [a for a in analyses if a.risk_level == RiskLevel.MEDIUM]

        summary = f"""
# CS2 BETTING OPPORTUNITIES - EXECUTIVE SUMMARY
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M UTC')}

## SUMMARY STATISTICS
Total Matches Analyzed: {len(analyses)}
Ultra-Low Risk Opportunities: {len(ultra_low)}
Low Risk Opportunities: {len(low)}
Medium Risk Opportunities: {len(medium)}

## TOP RECOMMENDATIONS
"""

        # Show top 3 recommendations
        top_recommendations = analyses[:3]
        for i, analysis in enumerate(top_recommendations, 1):
            risk_emoji = RISK_LEVELS[analysis.risk_level.value]
            summary += f"""
### RANK #{i}: {analysis.match_info.team_a} vs {analysis.match_info.team_b}
Tournament: {analysis.match_info.tournament}
Risk Level: {risk_emoji} {analysis.risk_level.value.title()}
Confidence: {analysis.confidence:.1%} | Expected Value: {analysis.expected_value:+.1%}
Recommendation: {analysis.recommended_bet_type.title()} on {analysis.recommended_selection}
Stake: {analysis.recommended_stake_percentage:.1%} of bankroll
"""

        return summary

async def main():
    """Main execution function"""

    # Example usage
    system = CS2BettingAnalysisSystem()

    # Example HLTV match URL (replace with actual URL)
    example_url = "https://www.hltv.org/matches/2382724/cybershoke-vs-nexus-galaxy-battle-2025-phase-2"

    print("CS2 Betting Analysis System")
    print("=" * 50)

    # Get match URL from user input
    match_url = input(f"Enter HLTV match URL (or press Enter for example): ").strip()
    if not match_url:
        match_url = example_url
        print(f"Using example URL: {match_url}")

    # Analyze the match
    print("\nAnalyzing match...")
    result = await system.analyze_match_from_url(match_url)

    if result:
        # Display formatted output
        formatted_output = system.format_analysis_output(result)
        print(formatted_output)

        # Save to file
        filename = f"cs2_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(formatted_output)
        print(f"\nAnalysis saved to: {filename}")

    else:
        print("Failed to analyze match. Check logs for details.")

if __name__ == "__main__":
    asyncio.run(main())
