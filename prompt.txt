I am trying to figure out a way to scrap match data from hltv.org such as https://www.hltv.org/matches/2382724/cybershoke-vs-nexus-galaxy-battle-2025-phase-2 to get a accurate prediction based on all of our real stats/data we find on the webiste following our prompt @cs-bet-claude-update.txt but we have been running into 403 forbidden errors because HLTV has cloudflare protectino such as captch<PERSON> and allowing cookies how do we prevent this? Make a script that will follow my prompt and analyze the matches to get the most accurate CS2 prediction as possible use UP-TO-DATE dependicies  if you choose to use python such as https://playwright.dev/python/docs/intro





I need help creating a web scraping script to extract Counter-Strike 2 match data from HLTV.org for betting predictions. Here are the specific requirements:

**Objective:**
Create a Python script that can scrape match data from HLTV.org URLs (example: https://www.hltv.org/matches/2382724/cybershoke-vs-nexus-galaxy-battle-2025-phase-2) and analyze the data to generate accurate CS2 match predictions.

**Technical Challenges to Solve:**
- Bypass Cloudflare protection (captcha, cookie requirements, bot detection)
- Handle 403 Forbidden errors we're currently encountering
- Use modern, up-to-date dependencies

**Requirements:**
1. Use Python with Playwright (https://playwright.dev/python/docs/intro) or similar modern web scraping tools
2. Implement proper anti-detection measures (user agents, delays, cookie handling)
3. Extract comprehensive match statistics and team data from HLTV pages
4. Follow the analysis methodology specified in the file `@cs-bet-claude-update.txt` (located in the workspace)
5. Generate accurate predictions based on the scraped real-time data
6. Use current/latest versions of all dependencies

**Deliverables:**
- A working Python script that can successfully scrape HLTV match pages
- Data extraction for team statistics, player performance, recent form, head-to-head records
- Prediction algorithm implementation following the specified prompt guidelines
- Error handling for rate limiting and anti-bot measures
- Documentation on how to run and configure the script

Please first examine the @cs-bet-claude-update.txt file to understand the specific prediction methodology, then create a robust scraping solution that can reliably access HLTV data without triggering their protection systems.