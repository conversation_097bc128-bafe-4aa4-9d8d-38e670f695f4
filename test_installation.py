#!/usr/bin/env python3
"""
Quick test script for CS2 HLTV Betting Analysis System
"""

import asyncio
import sys

async def test_basic_functionality():
    """Test basic functionality"""
    print("🧪 Testing CS2 HLTV Betting Analysis System...")
    
    try:
        # Test imports
        from main import CS2BettingAnalysisSystem
        from hltv_scraper import HLTVS<PERSON>raper
        from analysis_engine import CS2BettingAnalyzer
        print("✅ All core modules imported successfully")
        
        # Test analyzer
        analyzer = CS2BettingAnalyzer()
        print("✅ Analysis engine initialized")
        
        # Test system
        system = CS2BettingAnalysisSystem()
        print("✅ Main system initialized")
        
        print("\n🎉 Basic functionality test PASSED!")
        print("\n🚀 Ready to analyze CS2 matches!")
        print("\nRun: python main.py")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("\n🔧 Try running: python install_dependencies.py")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(test_basic_functionality())
