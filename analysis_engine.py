"""
CS2 Betting Analysis Engine
Implements the comprehensive analysis framework from cs-bet-claude-update.txt
"""

import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
import logging

from config import ANALYSIS_CONFIG, DATA_CONFIG, BET_TYPES, RIS<PERSON>_LEVELS
from data_models import (
    Team, MatchInfo, HeadToHead, AnalysisResult,
    RiskLevel, MatchFormat, BettingOdds
)
from utils import (
    calculate_win_rate, calculate_confidence_interval,
    weighted_average, safe_divide
)

logger = logging.getLogger(__name__)

class CS2BettingAnalyzer:
    """Main analysis engine for CS2 betting predictions"""

    def __init__(self):
        self.confidence_weights = {
            'recent_form': ANALYSIS_CONFIG.RECENT_FORM_WEIGHT,
            'head_to_head': ANALYSIS_CONFIG.HEAD_TO_HEAD_WEIGHT,
            'map_pool': ANALYSIS_CONFIG.MAP_POOL_WEIGHT,
            'player_performance': ANALYSIS_CONFIG.PLAYER_PERFORMANCE_WEIGHT,
            'tactical_factors': ANALYSIS_CONFIG.TACTICAL_FACTORS_WEIGHT
        }

    def analyze_match(
        self,
        match_info: MatchInfo,
        team_a: Team,
        team_b: Team,
        head_to_head: HeadToHead,
        betting_odds: Optional[List[BettingOdds]] = None
    ) -> AnalysisResult:
        """
        Comprehensive match analysis following the framework methodology
        """
        try:
            logger.info(f"Analyzing match: {team_a.name} vs {team_b.name}")

            # Phase 1: Data Quality Assessment
            data_quality = self._assess_data_quality(team_a, team_b, head_to_head)

            if data_quality < 0.6:
                logger.warning(f"Low data quality ({data_quality:.2f}) for match analysis")

            # Phase 2: Quantitative Analysis
            performance_scores = self._calculate_performance_scores(team_a, team_b)
            map_pool_analysis = self._analyze_map_pool(team_a, team_b)
            player_analysis = self._analyze_player_matchups(team_a, team_b)
            h2h_analysis = self._analyze_head_to_head(head_to_head)

            # Phase 3: Risk Assessment
            risk_level = self._assess_risk_level(
                team_a, team_b, match_info, performance_scores
            )

            # Phase 4: Probability Calculation
            win_probability = self._calculate_win_probability(
                performance_scores, map_pool_analysis, player_analysis, h2h_analysis
            )

            # Phase 5: Bet Type Selection and EV Calculation
            recommended_bet = self._select_optimal_bet_type(
                team_a, team_b, match_info, win_probability, betting_odds
            )

            # Phase 6: Confidence and Stake Calculation
            confidence = self._calculate_confidence(
                risk_level, data_quality, performance_scores
            )

            stake_percentage = self._calculate_kelly_stake(
                win_probability, float(recommended_bet.get('odds', 2.0))
            )

            # Compile analysis result
            result = AnalysisResult(
                match_info=match_info,
                team_a_data=team_a,
                team_b_data=team_b,
                head_to_head=head_to_head,
                risk_level=risk_level,
                confidence=confidence,
                expected_value=float(recommended_bet.get('expected_value', 0.0)),
                recommended_bet_type=str(recommended_bet.get('bet_type', 'moneyline')),
                recommended_selection=str(recommended_bet.get('selection', team_a.name)),
                recommended_stake_percentage=stake_percentage,
                data_quality_score=data_quality
            )

            # Add detailed analysis
            result.statistical_foundation = self._compile_statistical_foundation(
                team_a, team_b, performance_scores
            )
            result.advanced_metrics = self._compile_advanced_metrics(
                team_a, team_b, player_analysis
            )
            result.tactical_analysis = self._compile_tactical_analysis(
                team_a, team_b, match_info
            )
            result.risk_factors = self._identify_risk_factors(
                team_a, team_b, match_info, data_quality
            )
            result.alternative_bets = self._generate_alternative_bets(
                team_a, team_b, match_info, win_probability, betting_odds
            )

            logger.info(f"Analysis completed: {risk_level.value} risk, {confidence:.1%} confidence")
            return result

        except Exception as e:
            logger.error(f"Error in match analysis: {e}")
            raise

    def _assess_data_quality(self, team_a: Team, team_b: Team, h2h: HeadToHead) -> float:
        """Assess overall data quality for analysis"""
        quality_factors = []

        # Team data completeness
        for team in [team_a, team_b]:
            team_quality = 0.0

            # Basic data
            if team.ranking > 0:
                team_quality += 0.2
            if team.elo_rating > 0:
                team_quality += 0.2
            if len(team.recent_form) >= DATA_CONFIG.MIN_RECENT_MATCHES:
                team_quality += 0.3
            if len(team.players) >= 5:
                team_quality += 0.2
            if len(team.map_stats) >= 3:
                team_quality += 0.1

            quality_factors.append(team_quality)

        # Head-to-head data
        h2h_quality = 0.0
        if h2h.total_matches > 0:
            h2h_quality = min(1.0, h2h.total_matches / 5)  # Max quality at 5+ matches

        quality_factors.append(h2h_quality)

        return sum(quality_factors) / len(quality_factors)

    def _calculate_performance_scores(self, team_a: Team, team_b: Team) -> Dict[str, Dict[str, float]]:
        """Calculate weighted performance scores for both teams"""
        scores = {}

        for team_name, team in [('team_a', team_a), ('team_b', team_b)]:
            # Recent form score (30% weight)
            recent_form_score = self._calculate_recent_form_score(team)

            # Ranking score (25% weight)
            ranking_score = self._calculate_ranking_score(team)

            # Performance metrics score (25% weight)
            performance_score = self._calculate_performance_metrics_score(team)

            # Map pool score (20% weight)
            map_pool_score = self._calculate_map_pool_score(team)

            # Weighted total
            total_score = (
                recent_form_score * 0.30 +
                ranking_score * 0.25 +
                performance_score * 0.25 +
                map_pool_score * 0.20
            )

            scores[team_name] = {
                'total': total_score,
                'recent_form': recent_form_score,
                'ranking': ranking_score,
                'performance': performance_score,
                'map_pool': map_pool_score
            }

        return scores

    def _calculate_recent_form_score(self, team: Team) -> float:
        """Calculate recent form score with recency weighting"""
        if not team.recent_form:
            return 0.5  # Neutral score for missing data

        # Apply recency weights: last 5 matches = 50%, next 5 = 30%, next 5 = 20%
        weights = []
        for i, result in enumerate(team.recent_form[:15]):  # Max 15 matches
            if i < 5:
                weights.append(0.5 / 5)  # 50% weight split among first 5
            elif i < 10:
                weights.append(0.3 / 5)  # 30% weight split among next 5
            else:
                weights.append(0.2 / 5)  # 20% weight split among last 5

        # Convert boolean results to numeric (1 for win, 0 for loss)
        numeric_results = [1.0 if result else 0.0 for result in team.recent_form[:len(weights)]]

        return weighted_average(numeric_results, weights)

    def _calculate_ranking_score(self, team: Team) -> float:
        """Calculate ranking-based score (higher ranking = higher score)"""
        if team.ranking <= 0:
            return 0.5  # Neutral for unranked teams

        # Convert ranking to score (top 10 teams get higher scores)
        if team.ranking <= 5:
            return 0.9 + (6 - team.ranking) * 0.02  # 0.9-1.0 for top 5
        elif team.ranking <= 10:
            return 0.8 + (11 - team.ranking) * 0.02  # 0.8-0.9 for 6-10
        elif team.ranking <= 20:
            return 0.6 + (21 - team.ranking) * 0.01  # 0.6-0.8 for 11-20
        elif team.ranking <= 30:
            return 0.4 + (31 - team.ranking) * 0.01  # 0.4-0.6 for 21-30
        else:
            return max(0.1, 0.4 - (team.ranking - 30) * 0.005)  # Decreasing for 30+

    def _calculate_performance_metrics_score(self, team: Team) -> float:
        """Calculate score based on advanced performance metrics"""
        metrics = []

        # Team rating
        if team.avg_team_rating > 0:
            rating_score = min(1.0, team.avg_team_rating / 1.2)  # Normalize to 1.2 max
            metrics.append(rating_score)

        # Opening duels
        if team.opening_duel_success_rate > 0:
            opening_score = team.opening_duel_success_rate
            metrics.append(opening_score)

        # Trade kills
        if team.trade_kill_efficiency > 0:
            trade_score = team.trade_kill_efficiency
            metrics.append(trade_score)

        # Economic efficiency
        if team.buy_round_win_rate > 0:
            buy_score = team.buy_round_win_rate
            metrics.append(buy_score)

        if team.force_buy_win_rate > 0:
            force_score = team.force_buy_win_rate
            metrics.append(force_score)

        return sum(metrics) / len(metrics) if metrics else 0.5

    def _calculate_map_pool_score(self, team: Team) -> float:
        """Calculate map pool strength score"""
        if not team.map_stats:
            return 0.5

        map_scores = []
        for map_name, stats in team.map_stats.items():
            if stats.wins + stats.losses >= DATA_CONFIG.MIN_MAP_SAMPLE_SIZE:
                map_scores.append(stats.win_rate)

        return sum(map_scores) / len(map_scores) if map_scores else 0.5

    def _analyze_map_pool(self, team_a: Team, team_b: Team) -> Dict[str, float]:
        """Analyze map pool advantages between teams"""
        map_advantages = {}

        # Get common maps with sufficient data
        common_maps = set(team_a.map_stats.keys()) & set(team_b.map_stats.keys())

        for map_name in common_maps:
            stats_a = team_a.map_stats[map_name]
            stats_b = team_b.map_stats[map_name]

            # Only analyze maps with sufficient sample size
            if (stats_a.wins + stats_a.losses >= DATA_CONFIG.MIN_MAP_SAMPLE_SIZE and
                stats_b.wins + stats_b.losses >= DATA_CONFIG.MIN_MAP_SAMPLE_SIZE):

                advantage = stats_a.win_rate - stats_b.win_rate
                map_advantages[map_name] = advantage

        return map_advantages

    def _analyze_player_matchups(self, team_a: Team, team_b: Team) -> Dict[str, float]:
        """Analyze individual player performance matchups"""
        analysis = {
            'avg_rating_diff': 0.0,
            'star_player_advantage': 0.0,
            'role_advantages': {}
        }

        if not team_a.players or not team_b.players:
            return analysis

        # Calculate average rating difference
        ratings_a = [p.rating_2_0 for p in team_a.players if p.rating_2_0 > 0]
        ratings_b = [p.rating_2_0 for p in team_b.players if p.rating_2_0 > 0]

        if ratings_a and ratings_b:
            avg_rating_a = sum(ratings_a) / len(ratings_a)
            avg_rating_b = sum(ratings_b) / len(ratings_b)
            analysis['avg_rating_diff'] = avg_rating_a - avg_rating_b

        # Find star players (highest rated)
        if ratings_a and ratings_b:
            star_a = max(ratings_a)
            star_b = max(ratings_b)
            analysis['star_player_advantage'] = star_a - star_b

        return analysis

    def _analyze_head_to_head(self, h2h: HeadToHead) -> Dict[str, Union[float, int]]:
        """Analyze head-to-head statistics"""
        if h2h.total_matches == 0:
            return {
                'win_rate_a': 0.5,
                'win_rate_b': 0.5,
                'total_matches': 0,
                'recency_factor': 1.0
            }

        win_rate_a = h2h.team_a_wins / h2h.total_matches
        win_rate_b = h2h.team_b_wins / h2h.total_matches

        # Apply recency factor if last meeting was recent
        recency_factor = 1.0
        if h2h.last_meeting:
            days_since = (datetime.now() - h2h.last_meeting).days
            if days_since <= 90:  # Recent meeting within 3 months
                recency_factor = 1.2
            elif days_since <= 180:  # Within 6 months
                recency_factor = 1.1

        return {
            'win_rate_a': win_rate_a,
            'win_rate_b': win_rate_b,
            'total_matches': h2h.total_matches,
            'recency_factor': recency_factor
        }

    def _assess_risk_level(
        self,
        team_a: Team,
        team_b: Team,
        match_info: MatchInfo,
        performance_scores: Dict[str, Dict[str, float]]
    ) -> RiskLevel:
        """Assess risk level based on framework criteria"""

        # Get ranking gap
        ranking_gap = abs(team_a.ranking - team_b.ranking) if team_a.ranking > 0 and team_b.ranking > 0 else 0

        # Get Elo gap
        elo_gap = abs(team_a.elo_rating - team_b.elo_rating) if team_a.elo_rating > 0 and team_b.elo_rating > 0 else 0

        # Calculate recent form for favored team
        favored_team = team_a if team_a.ranking < team_b.ranking else team_b
        recent_wins = sum(1 for result in favored_team.recent_form[:10] if result)
        recent_win_rate = recent_wins / min(len(favored_team.recent_form), 10) if favored_team.recent_form else 0

        # Check roster stability
        roster_stable = True
        for team in [team_a, team_b]:
            if team.last_roster_change:
                days_since_change = (datetime.now() - team.last_roster_change).days
                if days_since_change < 60:
                    roster_stable = False

        # Check for stand-ins
        has_standins = bool(team_a.stand_ins or team_b.stand_ins)

        # Ultra-Low Risk Assessment
        if (ranking_gap >= ANALYSIS_CONFIG.ULTRA_LOW_RISK_RANKING_GAP or
            elo_gap >= ANALYSIS_CONFIG.ULTRA_LOW_RISK_ELO_GAP):
            if (recent_win_rate >= ANALYSIS_CONFIG.ULTRA_LOW_RISK_WIN_RATE and
                roster_stable and
                not has_standins and
                match_info.match_format in [MatchFormat.BO3, MatchFormat.BO5]):
                return RiskLevel.ULTRA_LOW

        # Low Risk Assessment
        if (ranking_gap >= ANALYSIS_CONFIG.LOW_RISK_RANKING_GAP or
            elo_gap >= ANALYSIS_CONFIG.LOW_RISK_ELO_GAP):
            if (recent_win_rate >= ANALYSIS_CONFIG.LOW_RISK_WIN_RATE and
                roster_stable and
                len(team_a.stand_ins + team_b.stand_ins) <= 1):
                return RiskLevel.LOW

        # Medium Risk Assessment
        if (ranking_gap >= ANALYSIS_CONFIG.MEDIUM_RISK_RANKING_GAP and
            recent_win_rate >= ANALYSIS_CONFIG.MEDIUM_RISK_WIN_RATE):
            return RiskLevel.MEDIUM

        # Default to High Risk
        return RiskLevel.HIGH

    def _calculate_win_probability(
        self,
        performance_scores: Dict[str, Dict[str, float]],
        map_pool_analysis: Dict[str, float],
        player_analysis: Dict[str, float],
        h2h_analysis: Dict[str, Union[float, int]]
    ) -> float:
        """Calculate win probability using weighted factors"""

        # Base probability from performance scores
        score_diff = performance_scores['team_a']['total'] - performance_scores['team_b']['total']
        base_prob = 0.5 + (score_diff * 0.3)  # Scale difference

        # Map pool adjustment
        map_advantages = list(map_pool_analysis.values())
        if map_advantages:
            avg_map_advantage = sum(map_advantages) / len(map_advantages)
            base_prob += avg_map_advantage * 0.15

        # Player performance adjustment
        if player_analysis['avg_rating_diff'] != 0:
            base_prob += player_analysis['avg_rating_diff'] * 0.1

        # Head-to-head adjustment
        if h2h_analysis['total_matches'] > 0:
            h2h_advantage = h2h_analysis['win_rate_a'] - 0.5
            h2h_weight = min(0.2, h2h_analysis['total_matches'] * 0.04)  # Max 20% weight
            base_prob += h2h_advantage * h2h_weight * h2h_analysis['recency_factor']

        # Ensure probability is within valid range
        return max(0.05, min(0.95, base_prob))

    def _calculate_confidence(
        self,
        risk_level: RiskLevel,
        data_quality: float,
        performance_scores: Dict[str, Dict[str, float]]
    ) -> float:
        """Calculate confidence level based on risk and data quality"""

        # Base confidence from risk level
        base_confidence = {
            RiskLevel.ULTRA_LOW: 0.80,
            RiskLevel.LOW: 0.70,
            RiskLevel.MEDIUM: 0.60,
            RiskLevel.HIGH: 0.45
        }[risk_level]

        # Adjust for data quality
        confidence = base_confidence * data_quality

        # Adjust for performance score certainty
        score_diff = abs(performance_scores['team_a']['total'] - performance_scores['team_b']['total'])
        certainty_bonus = min(0.1, score_diff * 0.2)
        confidence += certainty_bonus

        # Apply framework limits
        max_confidence = {
            RiskLevel.ULTRA_LOW: ANALYSIS_CONFIG.ULTRA_LOW_RISK_CONFIDENCE_MAX,
            RiskLevel.LOW: ANALYSIS_CONFIG.LOW_RISK_CONFIDENCE_MAX,
            RiskLevel.MEDIUM: ANALYSIS_CONFIG.MEDIUM_RISK_CONFIDENCE_MAX,
            RiskLevel.HIGH: 0.55
        }[risk_level]

        return min(max_confidence, confidence)

    def _select_optimal_bet_type(
        self,
        team_a: Team,
        team_b: Team,
        match_info: MatchInfo,
        win_probability: float,
        betting_odds: Optional[List[BettingOdds]] = None
    ) -> Dict[str, Union[str, float]]:
        """Select optimal bet type based on analysis"""

        # Default to moneyline bet
        recommended_bet = {
            'bet_type': 'moneyline',
            'selection': team_a.name if win_probability > 0.5 else team_b.name,
            'odds': 2.0,  # Default odds
            'expected_value': 0.0
        }

        if betting_odds:
            # Calculate expected values for different bet types
            best_ev = -1.0

            for odds_data in betting_odds:
                # Moneyline EV
                if win_probability > 0.5:
                    ev = (win_probability * (odds_data.team_a_odds - 1)) - ((1 - win_probability) * 1)
                    if ev > best_ev:
                        best_ev = ev
                        recommended_bet.update({
                            'bet_type': 'moneyline',
                            'selection': team_a.name,
                            'odds': odds_data.team_a_odds,
                            'expected_value': ev
                        })
                else:
                    ev = ((1 - win_probability) * (odds_data.team_b_odds - 1)) - (win_probability * 1)
                    if ev > best_ev:
                        best_ev = ev
                        recommended_bet.update({
                            'bet_type': 'moneyline',
                            'selection': team_b.name,
                            'odds': odds_data.team_b_odds,
                            'expected_value': ev
                        })

        return recommended_bet

    def _calculate_kelly_stake(self, win_probability: float, odds: float) -> float:
        """Calculate optimal stake using Kelly Criterion"""
        if odds <= 1.0:
            return 0.0

        # Kelly formula: f = (bp - q) / b
        # where b = odds - 1, p = win probability, q = 1 - p
        b = odds - 1
        p = win_probability
        q = 1 - p

        kelly_fraction = (b * p - q) / b

        # Apply maximum stake limit
        return min(ANALYSIS_CONFIG.MAX_STAKE_PERCENTAGE, max(0.0, kelly_fraction))

    def _compile_statistical_foundation(
        self,
        team_a: Team,
        team_b: Team,
        performance_scores: Dict[str, Dict[str, float]]
    ) -> Dict[str, Union[str, float]]:
        """Compile statistical foundation for analysis"""
        return {
            'ranking_gap': abs(team_a.ranking - team_b.ranking) if team_a.ranking > 0 and team_b.ranking > 0 else 0,
            'team_a_ranking': team_a.ranking,
            'team_b_ranking': team_b.ranking,
            'team_a_recent_form': f"{sum(team_a.recent_form[:10])}-{len(team_a.recent_form[:10]) - sum(team_a.recent_form[:10])}",
            'team_b_recent_form': f"{sum(team_b.recent_form[:10])}-{len(team_b.recent_form[:10]) - sum(team_b.recent_form[:10])}",
            'team_a_performance_score': performance_scores['team_a']['total'],
            'team_b_performance_score': performance_scores['team_b']['total']
        }

    def _compile_advanced_metrics(
        self,
        team_a: Team,
        team_b: Team,
        player_analysis: Dict[str, float]
    ) -> Dict[str, float]:
        """Compile advanced performance metrics"""
        return {
            'team_a_avg_rating': team_a.avg_team_rating,
            'team_b_avg_rating': team_b.avg_team_rating,
            'team_a_opening_duels': team_a.opening_duel_success_rate,
            'team_b_opening_duels': team_b.opening_duel_success_rate,
            'team_a_trade_efficiency': team_a.trade_kill_efficiency,
            'team_b_trade_efficiency': team_b.trade_kill_efficiency,
            'rating_difference': player_analysis.get('avg_rating_diff', 0.0),
            'star_player_advantage': player_analysis.get('star_player_advantage', 0.0)
        }

    def _compile_tactical_analysis(
        self,
        team_a: Team,
        team_b: Team,
        match_info: MatchInfo
    ) -> Dict[str, str]:
        """Compile tactical analysis factors"""
        analysis = {}

        # Roster stability
        if team_a.last_roster_change:
            days_since = (datetime.now() - team_a.last_roster_change).days
            analysis['team_a_roster_stability'] = f"Last change {days_since} days ago"
        else:
            analysis['team_a_roster_stability'] = "Stable roster"

        if team_b.last_roster_change:
            days_since = (datetime.now() - team_b.last_roster_change).days
            analysis['team_b_roster_stability'] = f"Last change {days_since} days ago"
        else:
            analysis['team_b_roster_stability'] = "Stable roster"

        # Stand-ins
        analysis['team_a_standins'] = f"{len(team_a.stand_ins)} stand-ins" if team_a.stand_ins else "No stand-ins"
        analysis['team_b_standins'] = f"{len(team_b.stand_ins)} stand-ins" if team_b.stand_ins else "No stand-ins"

        # Match format impact
        analysis['match_format'] = f"{match_info.match_format.value} format"
        analysis['venue'] = match_info.venue

        return analysis

    def _identify_risk_factors(
        self,
        team_a: Team,
        team_b: Team,
        match_info: MatchInfo,
        data_quality: float
    ) -> List[str]:
        """Identify potential risk factors"""
        risk_factors = []

        if data_quality < 0.7:
            risk_factors.append(f"Low data quality ({data_quality:.1%})")

        if match_info.match_format == MatchFormat.BO1:
            risk_factors.append("BO1 format increases variance")

        if team_a.stand_ins or team_b.stand_ins:
            risk_factors.append("Stand-ins present")

        for team_name, team in [('Team A', team_a), ('Team B', team_b)]:
            if team.last_roster_change:
                days_since = (datetime.now() - team.last_roster_change).days
                if days_since < 30:
                    risk_factors.append(f"{team_name} recent roster change ({days_since} days)")

            if len(team.recent_form) < DATA_CONFIG.MIN_RECENT_MATCHES:
                risk_factors.append(f"{team_name} insufficient recent matches")

        return risk_factors

    def _generate_alternative_bets(
        self,
        team_a: Team,
        team_b: Team,
        match_info: MatchInfo,
        win_probability: float,
        betting_odds: Optional[List[BettingOdds]] = None
    ) -> List[Dict[str, Union[str, float]]]:
        """Generate alternative betting options"""
        alternatives = []

        # Map handicap options
        if match_info.match_format in [MatchFormat.BO3, MatchFormat.BO5]:
            # Analyze map pool for handicap betting
            map_advantages = self._analyze_map_pool(team_a, team_b)
            if map_advantages:
                avg_advantage = sum(map_advantages.values()) / len(map_advantages)
                if abs(avg_advantage) > 0.2:  # Significant map pool advantage
                    alternatives.append({
                        'bet_type': 'map_handicap',
                        'selection': f"{team_a.name if avg_advantage > 0 else team_b.name} -1.5",
                        'rationale': 'Strong map pool advantage',
                        'expected_value': 0.0  # Would need odds to calculate
                    })

        # Total maps over/under
        if match_info.match_format == MatchFormat.BO3:
            # Predict match length based on team strengths
            strength_diff = abs(win_probability - 0.5)
            if strength_diff > 0.2:  # Large skill gap
                alternatives.append({
                    'bet_type': 'total_maps',
                    'selection': 'Under 2.5',
                    'rationale': 'Large skill gap suggests 2-0 result',
                    'expected_value': 0.0
                })
            elif strength_diff < 0.1:  # Very close match
                alternatives.append({
                    'bet_type': 'total_maps',
                    'selection': 'Over 2.5',
                    'rationale': 'Close matchup likely to go 3 maps',
                    'expected_value': 0.0
                })

        return alternatives
