"""
HLTV Web Scraper for CS2 Match Data
Handles Cloudflare protection and extracts comprehensive match statistics
Uses modern NoDriver for advanced anti-detection
"""

import asyncio
import json
import re
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from urllib.parse import urljoin, urlparse

# Modern anti-detection imports
try:
    import nodriver as uc
except ImportError:
    uc = None

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from bs4 import BeautifulSoup

try:
    import cloudscraper
except ImportError:
    cloudscraper = None

import logging

from config import SCRAPING_CONFIG, URL_CONFIG, CS2_MAPS
from data_models import (
    Team, Player, MatchInfo, MapStats, HeadToHead,
    BettingOdds, ScrapingResult, MatchFormat
)
from utils import (
    random_delay, get_random_user_agent, clean_text,
    parse_percentage, parse_rating, parse_ranking,
    normalize_team_name, normalize_map_name, parse_match_time,
    extract_match_id_from_url, build_hltv_url, safe_divide
)

logger = logging.getLogger(__name__)

class HLTVScraper:
    """Main scraper class for HLTV.org data extraction"""

    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.session_cookies: Dict = {}

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()

    async def initialize_browser(self) -> None:
        """Initialize Playwright browser with anti-detection measures"""
        try:
            playwright = await async_playwright().start()

            # Launch browser with stealth settings
            self.browser = await playwright.chromium.launch(
                headless=SCRAPING_CONFIG.HEADLESS,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--user-agent=' + get_random_user_agent()
                ]
            )

            # Create context with realistic settings
            self.context = await self.browser.new_context(
                viewport={
                    'width': SCRAPING_CONFIG.VIEWPORT_WIDTH,
                    'height': SCRAPING_CONFIG.VIEWPORT_HEIGHT
                },
                user_agent=get_random_user_agent(),
                locale='en-US',
                timezone_id='America/New_York',
                permissions=['geolocation'],
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
            )

            # Create page
            self.page = await self.context.new_page()

            # Add stealth scripts
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });

                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });

                window.chrome = {
                    runtime: {},
                };
            """)

            logger.info("Browser initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise

    async def close(self) -> None:
        """Close browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            logger.info("Browser closed successfully")
        except Exception as e:
            logger.error(f"Error closing browser: {e}")

    async def navigate_with_retry(self, url: str, max_retries: int = 3) -> bool:
        """Navigate to URL with retry logic and Cloudflare handling"""
        for attempt in range(max_retries):
            try:
                logger.info(f"Navigating to {url} (attempt {attempt + 1})")

                # Add random delay before navigation
                await asyncio.sleep(random.uniform(1, 3))

                # Navigate to page
                response = await self.page.goto(
                    url,
                    wait_until='domcontentloaded',
                    timeout=SCRAPING_CONFIG.PAGE_LOAD_TIMEOUT * 1000
                )

                if response.status == 403:
                    logger.warning(f"403 Forbidden received for {url}")
                    await self.handle_cloudflare_challenge()
                    continue

                # Check for Cloudflare challenge
                if await self.is_cloudflare_challenge():
                    logger.info("Cloudflare challenge detected, waiting...")
                    await self.handle_cloudflare_challenge()

                # Wait for page to be fully loaded
                await self.page.wait_for_load_state('networkidle', timeout=30000)

                # Verify we're on the correct page
                current_url = self.page.url
                if urlparse(current_url).netloc == urlparse(url).netloc:
                    logger.info(f"Successfully navigated to {url}")
                    return True

            except Exception as e:
                logger.error(f"Navigation attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(random.uniform(5, 10))

        logger.error(f"Failed to navigate to {url} after {max_retries} attempts")
        return False

    async def is_cloudflare_challenge(self) -> bool:
        """Check if current page is a Cloudflare challenge"""
        try:
            # Check for common Cloudflare indicators
            cloudflare_indicators = [
                'cf-browser-verification',
                'cf-challenge-running',
                'Checking your browser',
                'DDoS protection by Cloudflare',
                'Ray ID'
            ]

            page_content = await self.page.content()

            for indicator in cloudflare_indicators:
                if indicator in page_content:
                    return True

            # Check for Cloudflare JavaScript challenge
            cf_scripts = await self.page.query_selector_all('script[src*="cloudflare"]')
            if cf_scripts:
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking for Cloudflare challenge: {e}")
            return False

    async def handle_cloudflare_challenge(self) -> None:
        """Handle Cloudflare challenge by waiting and simulating human behavior"""
        try:
            logger.info("Handling Cloudflare challenge...")

            # Wait for challenge to complete (usually 5-10 seconds)
            await asyncio.sleep(random.uniform(8, 15))

            # Simulate human-like mouse movements
            await self.simulate_human_behavior()

            # Wait for redirect after challenge completion
            try:
                await self.page.wait_for_load_state('networkidle', timeout=30000)
            except:
                pass  # Timeout is acceptable here

            # Additional wait to ensure page is fully loaded
            await asyncio.sleep(random.uniform(2, 5))

            logger.info("Cloudflare challenge handling completed")

        except Exception as e:
            logger.error(f"Error handling Cloudflare challenge: {e}")

    async def simulate_human_behavior(self) -> None:
        """Simulate human-like behavior to avoid detection"""
        try:
            # Random mouse movements
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, SCRAPING_CONFIG.VIEWPORT_WIDTH - 100)
                y = random.randint(100, SCRAPING_CONFIG.VIEWPORT_HEIGHT - 100)
                await self.page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.1, 0.3))

            # Random scroll
            await self.page.evaluate(f"window.scrollTo(0, {random.randint(100, 500)})")
            await asyncio.sleep(random.uniform(0.5, 1.0))

        except Exception as e:
            logger.error(f"Error simulating human behavior: {e}")

    async def get_page_content(self, url: str) -> Optional[str]:
        """Get page content with error handling"""
        try:
            if not await self.navigate_with_retry(url):
                return None

            content = await self.page.content()
            return content

        except Exception as e:
            logger.error(f"Error getting page content from {url}: {e}")
            return None

    async def scrape_match_info(self, match_url: str) -> ScrapingResult:
        """Scrape basic match information from HLTV match page"""
        start_time = datetime.now()

        try:
            content = await self.get_page_content(match_url)
            if not content:
                return ScrapingResult(
                    success=False,
                    error_message="Failed to get page content",
                    source_url=match_url
                )

            soup = BeautifulSoup(content, 'html.parser')

            # Extract match ID from URL
            match_id = extract_match_id_from_url(match_url) or ""

            # Extract team names
            team_elements = soup.find_all('div', class_='teamName')
            if len(team_elements) < 2:
                return ScrapingResult(
                    success=False,
                    error_message="Could not find team names",
                    source_url=match_url
                )

            team_a = clean_text(team_elements[0].get_text())
            team_b = clean_text(team_elements[1].get_text())

            # Extract tournament info
            tournament_element = soup.find('div', class_='event')
            tournament = clean_text(tournament_element.get_text()) if tournament_element else ""

            # Extract match format
            format_element = soup.find('div', class_='padding')
            format_text = clean_text(format_element.get_text()) if format_element else ""

            match_format = MatchFormat.BO3  # Default
            if 'bo1' in format_text.lower():
                match_format = MatchFormat.BO1
            elif 'bo5' in format_text.lower():
                match_format = MatchFormat.BO5

            # Extract scheduled time
            time_element = soup.find('div', class_='time')
            scheduled_time = None
            if time_element:
                time_text = clean_text(time_element.get_text())
                scheduled_time = parse_match_time(time_text)

            # Extract venue (LAN/Online)
            venue = "Online"  # Default
            venue_indicators = soup.find_all(text=re.compile(r'LAN|Online', re.I))
            if venue_indicators:
                venue = "LAN" if any('lan' in indicator.lower() for indicator in venue_indicators) else "Online"

            # Create match info object
            match_info = MatchInfo(
                match_id=match_id,
                team_a=team_a,
                team_b=team_b,
                tournament=tournament,
                match_format=match_format,
                scheduled_time=scheduled_time or datetime.now(),
                venue=venue,
                hltv_url=match_url
            )

            response_time = (datetime.now() - start_time).total_seconds()

            return ScrapingResult(
                success=True,
                data=match_info,
                source_url=match_url,
                response_time=response_time
            )

        except Exception as e:
            logger.error(f"Error scraping match info from {match_url}: {e}")
            return ScrapingResult(
                success=False,
                error_message=str(e),
                source_url=match_url,
                response_time=(datetime.now() - start_time).total_seconds()
            )

    async def scrape_team_ranking(self, team_name: str) -> ScrapingResult:
        """Scrape team ranking from HLTV rankings page"""
        start_time = datetime.now()

        try:
            content = await self.get_page_content(URL_CONFIG.HLTV_RANKINGS)
            if not content:
                return ScrapingResult(
                    success=False,
                    error_message="Failed to get rankings page",
                    source_url=URL_CONFIG.HLTV_RANKINGS
                )

            soup = BeautifulSoup(content, 'html.parser')

            # Find team in rankings
            team_rows = soup.find_all('div', class_='ranked-team')

            for row in team_rows:
                team_element = row.find('span', class_='name')
                if team_element:
                    found_team = clean_text(team_element.get_text())
                    if normalize_team_name(found_team) == normalize_team_name(team_name):
                        # Extract ranking
                        rank_element = row.find('span', class_='position')
                        ranking = parse_ranking(rank_element.get_text()) if rank_element else 0

                        # Extract points (Elo-like rating)
                        points_element = row.find('span', class_='points')
                        points = int(clean_text(points_element.get_text())) if points_element else 0

                        team_data = Team(
                            name=found_team,
                            ranking=ranking,
                            elo_rating=points
                        )

                        response_time = (datetime.now() - start_time).total_seconds()

                        return ScrapingResult(
                            success=True,
                            data=team_data,
                            source_url=URL_CONFIG.HLTV_RANKINGS,
                            response_time=response_time
                        )

            # Team not found in rankings
            return ScrapingResult(
                success=False,
                error_message=f"Team '{team_name}' not found in rankings",
                source_url=URL_CONFIG.HLTV_RANKINGS,
                response_time=(datetime.now() - start_time).total_seconds()
            )

        except Exception as e:
            logger.error(f"Error scraping team ranking for {team_name}: {e}")
            return ScrapingResult(
                success=False,
                error_message=str(e),
                source_url=URL_CONFIG.HLTV_RANKINGS,
                response_time=(datetime.now() - start_time).total_seconds()
            )
