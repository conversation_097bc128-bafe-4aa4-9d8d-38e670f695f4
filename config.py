"""
Configuration settings for HLTV CS2 betting analysis scraper
"""

import os
from typing import Dict, List
from dataclasses import dataclass

@dataclass
class ScrapingConfig:
    """Configuration for web scraping settings"""
    
    # Anti-detection settings
    MIN_DELAY: float = 2.0
    MAX_DELAY: float = 5.0
    REQUEST_TIMEOUT: int = 30
    PAGE_LOAD_TIMEOUT: int = 60
    
    # Browser settings
    HEADLESS: bool = True
    VIEWPORT_WIDTH: int = 1920
    VIEWPORT_HEIGHT: int = 1080
    
    # User agents for rotation
    USER_AGENTS: List[str] = None
    
    def __post_init__(self):
        if self.USER_AGENTS is None:
            self.USER_AGENTS = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]

@dataclass
class URLConfig:
    """HLTV and other source URLs"""
    
    # Primary HLTV URLs
    HLTV_BASE: str = "https://www.hltv.org"
    HLTV_MATCHES: str = "https://www.hltv.org/matches"
    HLTV_RANKINGS: str = "https://www.hltv.org/ranking/teams"
    HLTV_BETTING: str = "https://www.hltv.org/betting/analytics"
    HLTV_PLAYER_STATS: str = "https://www.hltv.org/stats/players"
    HLTV_TEAM_STATS: str = "https://www.hltv.org/stats/teams"
    HLTV_HEAD_TO_HEAD: str = "https://www.hltv.org/stats/teams/head-to-head"
    
    # Fallback sources
    LIQUIPEDIA_BASE: str = "https://liquipedia.net/counterstrike"
    BO3_BASE: str = "https://bo3.gg"
    DUST2_BASE: str = "https://www.dust2.in"

@dataclass
class AnalysisConfig:
    """Configuration for betting analysis"""
    
    # Risk thresholds
    ULTRA_LOW_RISK_RANKING_GAP: int = 15
    ULTRA_LOW_RISK_ELO_GAP: int = 200
    ULTRA_LOW_RISK_WIN_RATE: float = 0.75
    ULTRA_LOW_RISK_CONFIDENCE_MIN: float = 0.75
    ULTRA_LOW_RISK_CONFIDENCE_MAX: float = 0.85
    
    LOW_RISK_RANKING_GAP: int = 8
    LOW_RISK_ELO_GAP: int = 150
    LOW_RISK_WIN_RATE: float = 0.65
    LOW_RISK_CONFIDENCE_MIN: float = 0.65
    LOW_RISK_CONFIDENCE_MAX: float = 0.75
    
    MEDIUM_RISK_RANKING_GAP: int = 5
    MEDIUM_RISK_WIN_RATE: float = 0.55
    MEDIUM_RISK_CONFIDENCE_MIN: float = 0.55
    MEDIUM_RISK_CONFIDENCE_MAX: float = 0.65
    
    # Expected value thresholds
    MIN_EV_LOW_RISK: float = 0.10
    MIN_EV_MEDIUM_RISK: float = 0.15
    
    # Kelly Criterion settings
    MAX_STAKE_PERCENTAGE: float = 0.05  # 5% max bankroll per bet
    
    # Statistical weights
    RECENT_FORM_WEIGHT: float = 0.30
    HEAD_TO_HEAD_WEIGHT: float = 0.20
    MAP_POOL_WEIGHT: float = 0.25
    PLAYER_PERFORMANCE_WEIGHT: float = 0.15
    TACTICAL_FACTORS_WEIGHT: float = 0.10

@dataclass
class DataConfig:
    """Configuration for data collection and validation"""
    
    # Minimum sample sizes
    MIN_RECENT_MATCHES: int = 5
    MIN_MAP_SAMPLE_SIZE: int = 5
    MIN_PLAYER_MAPS: int = 20
    
    # Data freshness requirements
    MAX_DATA_AGE_HOURS: int = 24
    LINEUP_VERIFICATION_HOURS: int = 24
    
    # Cross-validation requirements
    MIN_SOURCES_FOR_VERIFICATION: int = 3
    
    # Performance thresholds
    MIN_TEAM_RATING: float = 1.08
    MIN_OPENING_DUEL_RATE: float = 0.60
    MIN_TRADE_KILL_EFFICIENCY: float = 0.75
    MIN_BUY_ROUND_WIN_RATE: float = 0.60
    MIN_FORCE_BUY_WIN_RATE: float = 0.30
    MIN_CLUTCH_SUCCESS_RATE: float = 0.35

# Global configuration instances
SCRAPING_CONFIG = ScrapingConfig()
URL_CONFIG = URLConfig()
ANALYSIS_CONFIG = AnalysisConfig()
DATA_CONFIG = DataConfig()

# CS2 Maps
CS2_MAPS = [
    "mirage", "inferno", "dust2", "nuke", "overpass", 
    "vertigo", "ancient", "anubis"
]

# Bet types
BET_TYPES = {
    "moneyline": "Match Winner",
    "map_handicap": "Map Handicap",
    "total_maps": "Total Maps",
    "correct_score": "Correct Score",
    "map_winner": "Map Winner",
    "player_performance": "Player Performance",
    "specialty": "Specialty Markets"
}

# Risk levels
RISK_LEVELS = {
    "ultra_low": "🛡️ Ultra-Low",
    "low": "⚖️ Low", 
    "medium": "⚠️ Medium",
    "high": "🚫 High"
}
