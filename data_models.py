"""
Data models for CS2 betting analysis
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union
from datetime import datetime
from enum import Enum

class MatchFormat(Enum):
    BO1 = "BO1"
    BO3 = "BO3"
    BO5 = "BO5"

class RiskLevel(Enum):
    ULTRA_LOW = "ultra_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

@dataclass
class Player:
    """Individual player data"""
    name: str
    nickname: str
    rating_2_0: float = 0.0
    adr: float = 0.0  # Average Damage per Round
    kast: float = 0.0  # Kill, Assist, Survive, Trade percentage
    impact: float = 0.0
    opening_kill_rating: float = 0.0
    clutch_success_rate: float = 0.0
    maps_played: int = 0
    role: str = ""  # Entry, Support, AWP, IGL, etc.

@dataclass
class MapStats:
    """Map-specific statistics"""
    map_name: str
    wins: int = 0
    losses: int = 0
    win_rate: float = 0.0
    ct_win_rate: float = 0.0
    t_win_rate: float = 0.0
    pistol_round_win_rate: float = 0.0
    avg_rounds_won: float = 0.0
    avg_rounds_lost: float = 0.0

@dataclass
class Team:
    """Team data and statistics"""
    name: str
    ranking: int = 0
    elo_rating: int = 0
    players: List[Player] = field(default_factory=list)
    recent_form: List[bool] = field(default_factory=list)  # True for wins, False for losses
    map_stats: Dict[str, MapStats] = field(default_factory=dict)
    
    # Performance metrics
    avg_team_rating: float = 0.0
    opening_duel_success_rate: float = 0.0
    trade_kill_efficiency: float = 0.0
    buy_round_win_rate: float = 0.0
    force_buy_win_rate: float = 0.0
    
    # Meta information
    last_roster_change: Optional[datetime] = None
    stand_ins: List[str] = field(default_factory=list)
    coach: str = ""

@dataclass
class HeadToHead:
    """Head-to-head statistics between two teams"""
    team_a_wins: int = 0
    team_b_wins: int = 0
    total_matches: int = 0
    map_specific_h2h: Dict[str, Dict[str, int]] = field(default_factory=dict)
    last_meeting: Optional[datetime] = None
    avg_match_length: float = 0.0  # Average maps per match

@dataclass
class MatchInfo:
    """Basic match information"""
    match_id: str
    team_a: str
    team_b: str
    tournament: str
    match_format: MatchFormat
    scheduled_time: datetime
    venue: str = ""  # LAN or Online
    stage: str = ""  # Group, Playoffs, etc.
    
    # URLs for data sources
    hltv_url: str = ""
    liquipedia_url: str = ""
    bo3_url: str = ""

@dataclass
class BettingOdds:
    """Betting odds from various sources"""
    bookmaker: str
    team_a_odds: float = 0.0
    team_b_odds: float = 0.0
    map_handicap_odds: Dict[str, float] = field(default_factory=dict)
    total_maps_odds: Dict[str, float] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class AnalysisResult:
    """Result of betting analysis"""
    match_info: MatchInfo
    team_a_data: Team
    team_b_data: Team
    head_to_head: HeadToHead
    
    # Analysis results
    risk_level: RiskLevel
    confidence: float
    expected_value: float
    recommended_bet_type: str
    recommended_selection: str
    recommended_stake_percentage: float
    
    # Detailed analysis
    statistical_foundation: Dict[str, Union[str, float]] = field(default_factory=dict)
    advanced_metrics: Dict[str, float] = field(default_factory=dict)
    tactical_analysis: Dict[str, str] = field(default_factory=dict)
    risk_factors: List[str] = field(default_factory=list)
    red_flags: List[str] = field(default_factory=list)
    
    # Alternative betting options
    alternative_bets: List[Dict[str, Union[str, float]]] = field(default_factory=list)
    
    # Data quality metrics
    data_quality_score: float = 0.0
    sources_verified: int = 0
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class ScrapingResult:
    """Result of web scraping operation"""
    success: bool
    data: Optional[Union[Team, MatchInfo, List]] = None
    error_message: str = ""
    source_url: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    response_time: float = 0.0

@dataclass
class ValidationResult:
    """Result of data validation"""
    is_valid: bool
    confidence_score: float
    conflicting_sources: List[str] = field(default_factory=list)
    missing_data: List[str] = field(default_factory=list)
    data_age_hours: float = 0.0
