"""
Enhanced HLTV Web Scraper for CS2 Match Data
Uses modern NoDriver and multiple anti-detection techniques
"""

import asyncio
import random
import time
from datetime import datetime
from typing import Dict, List, Optional, Union
from urllib.parse import urlparse

# Modern anti-detection imports
import nodriver as uc
import cloudscraper
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async
from bs4 import BeautifulSoup
import logging

from config import SCRAPING_CONFIG, URL_CONFIG
from data_models import Team, MatchInfo, ScrapingResult, MatchFormat
from utils import (
    clean_text, parse_ranking, normalize_team_name,
    parse_match_time, extract_match_id_from_url
)

logger = logging.getLogger(__name__)

class EnhancedHLTVScraper:
    """Enhanced scraper with multiple anti-detection methods"""

    def __init__(self):
        self.session = None
        self.browser = None
        self.page = None

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()

    async def close(self):
        """Close all connections"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if self.session:
                self.session.close()
        except Exception as e:
            logger.error(f"Error closing connections: {e}")

    async def scrape_with_nodriver(self, url: str) -> Optional[str]:
        """Scrape using NoDriver (most advanced anti-detection)"""
        try:
            logger.info("🚀 Using NoDriver for advanced anti-detection")

            # Configure NoDriver options
            options = uc.Config()
            options.sandbox = False
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')

            # Start browser
            browser = await uc.start(config=options)

            # Navigate to page
            page = await browser.get(url)

            # Wait for page to load
            await asyncio.sleep(random.uniform(3, 6))

            # Get page content
            content = await page.get_content()

            # Close browser
            await browser.stop()

            logger.info("✅ NoDriver scraping successful")
            return content

        except Exception as e:
            logger.error(f"❌ NoDriver scraping failed: {e}")
            return None

    async def scrape_with_playwright_stealth(self, url: str) -> Optional[str]:
        """Scrape using Playwright with stealth mode"""
        try:
            logger.info("🎭 Using Playwright with stealth mode")

            playwright = await async_playwright().start()

            # Launch browser with stealth settings
            browser = await playwright.chromium.launch(
                headless=SCRAPING_CONFIG.HEADLESS,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-gpu',
                    '--disable-web-security'
                ]
            )

            # Create context with realistic settings
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York'
            )

            # Create page and apply stealth
            page = await context.new_page()
            await stealth_async(page)

            # Navigate with realistic behavior
            await page.goto(url, wait_until='domcontentloaded', timeout=60000)

            # Simulate human behavior
            await self._simulate_human_behavior(page)

            # Wait for content to load
            await page.wait_for_load_state('networkidle', timeout=30000)

            # Get content
            content = await page.content()

            # Cleanup
            await browser.close()
            await playwright.stop()

            logger.info("✅ Playwright stealth scraping successful")
            return content

        except Exception as e:
            logger.error(f"❌ Playwright stealth scraping failed: {e}")
            return None

    def scrape_with_cloudscraper(self, url: str) -> Optional[str]:
        """Scrape using CloudScraper (HTTP-based)"""
        try:
            logger.info("☁️ Using CloudScraper for HTTP requests")

            # Create CloudScraper session
            scraper = cloudscraper.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'desktop': True
                }
            )

            # Add realistic headers
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            # Make request with delay
            time.sleep(random.uniform(2, 4))
            response = scraper.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                logger.info("✅ CloudScraper scraping successful")
                return response.text
            else:
                logger.warning(f"⚠️ CloudScraper got status code: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ CloudScraper scraping failed: {e}")
            return None

    async def _simulate_human_behavior(self, page):
        """Simulate realistic human behavior"""
        try:
            # Random mouse movements
            for _ in range(random.randint(2, 4)):
                x = random.randint(100, 1800)
                y = random.randint(100, 900)
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.1, 0.3))

            # Random scroll
            scroll_y = random.randint(100, 800)
            await page.evaluate(f"window.scrollTo(0, {scroll_y})")
            await asyncio.sleep(random.uniform(1, 2))

            # Random click (safe area)
            await page.mouse.click(random.randint(500, 1000), random.randint(200, 400))
            await asyncio.sleep(random.uniform(0.5, 1.0))

        except Exception as e:
            logger.debug(f"Human behavior simulation error: {e}")

    async def get_page_content(self, url: str) -> Optional[str]:
        """Get page content using multiple methods with fallback"""

        methods = [
            ("NoDriver", self.scrape_with_nodriver),
            ("Playwright Stealth", self.scrape_with_playwright_stealth),
            ("CloudScraper", lambda u: self.scrape_with_cloudscraper(u))
        ]

        for method_name, method in methods:
            try:
                logger.info(f"🔄 Attempting {method_name}...")

                if asyncio.iscoroutinefunction(method):
                    content = await method(url)
                else:
                    content = method(url)

                if content and len(content) > 1000:  # Basic content validation
                    logger.info(f"✅ Successfully scraped with {method_name}")
                    return content
                else:
                    logger.warning(f"⚠️ {method_name} returned insufficient content")

            except Exception as e:
                logger.error(f"❌ {method_name} failed: {e}")

            # Delay between attempts
            await asyncio.sleep(random.uniform(2, 5))

        logger.error("❌ All scraping methods failed")
        return None

    async def scrape_match_info(self, match_url: str) -> ScrapingResult:
        """Scrape match information with enhanced error handling"""
        start_time = datetime.now()

        try:
            logger.info(f"🎯 Scraping match info from: {match_url}")

            content = await self.get_page_content(match_url)
            if not content:
                return ScrapingResult(
                    success=False,
                    error_message="Failed to get page content with all methods",
                    source_url=match_url,
                    response_time=(datetime.now() - start_time).total_seconds()
                )

            # Parse content
            soup = BeautifulSoup(content, 'html.parser')

            # Extract match ID
            match_id = extract_match_id_from_url(match_url) or ""

            # Extract team names with multiple selectors
            team_names = []
            team_selectors = [
                '.teamName',
                '.team-name',
                '.match-team-name',
                '.team .name',
                '[class*="team"][class*="name"]'
            ]

            for selector in team_selectors:
                elements = soup.select(selector)
                if len(elements) >= 2:
                    team_names = [clean_text(elem.get_text()) for elem in elements[:2]]
                    break

            if len(team_names) < 2:
                # Fallback: try to find team names in title or other elements
                title = soup.find('title')
                if title and ' vs ' in title.get_text():
                    title_text = title.get_text()
                    if ' vs ' in title_text:
                        parts = title_text.split(' vs ')
                        if len(parts) >= 2:
                            team_names = [clean_text(parts[0]), clean_text(parts[1].split(' - ')[0])]

            if len(team_names) < 2:
                return ScrapingResult(
                    success=False,
                    error_message="Could not extract team names",
                    source_url=match_url,
                    response_time=(datetime.now() - start_time).total_seconds()
                )

            team_a, team_b = team_names[0], team_names[1]

            # Extract tournament info
            tournament_selectors = ['.event', '.tournament', '.event-name', '[class*="event"]']
            tournament = ""
            for selector in tournament_selectors:
                element = soup.select_one(selector)
                if element:
                    tournament = clean_text(element.get_text())
                    break

            # Extract match format
            format_text = content.lower()
            if 'bo1' in format_text or 'best of 1' in format_text:
                match_format = MatchFormat.BO1
            elif 'bo5' in format_text or 'best of 5' in format_text:
                match_format = MatchFormat.BO5
            else:
                match_format = MatchFormat.BO3  # Default

            # Extract venue
            venue = "Online"  # Default
            if any(indicator in content.lower() for indicator in ['lan', 'offline', 'arena', 'stadium']):
                venue = "LAN"

            # Create match info
            match_info = MatchInfo(
                match_id=match_id,
                team_a=team_a,
                team_b=team_b,
                tournament=tournament or "Unknown Tournament",
                match_format=match_format,
                scheduled_time=datetime.now(),  # Would need better parsing
                venue=venue,
                hltv_url=match_url
            )

            response_time = (datetime.now() - start_time).total_seconds()

            logger.info(f"✅ Successfully extracted: {team_a} vs {team_b}")

            return ScrapingResult(
                success=True,
                data=match_info,
                source_url=match_url,
                response_time=response_time
            )

        except Exception as e:
            logger.error(f"❌ Error scraping match info: {e}")
            return ScrapingResult(
                success=False,
                error_message=str(e),
                source_url=match_url,
                response_time=(datetime.now() - start_time).total_seconds()
            )

    async def scrape_team_ranking(self, team_name: str) -> ScrapingResult:
        """Scrape team ranking with enhanced methods"""
        start_time = datetime.now()

        try:
            logger.info(f"🏆 Scraping ranking for team: {team_name}")

            content = await self.get_page_content(URL_CONFIG.HLTV_RANKINGS)
            if not content:
                return ScrapingResult(
                    success=False,
                    error_message="Failed to get rankings page",
                    source_url=URL_CONFIG.HLTV_RANKINGS,
                    response_time=(datetime.now() - start_time).total_seconds()
                )

            soup = BeautifulSoup(content, 'html.parser')

            # Multiple selectors for team rankings
            ranking_selectors = [
                '.ranked-team',
                '.ranking-team',
                '[class*="rank"]',
                '.team-row'
            ]

            for selector in ranking_selectors:
                team_rows = soup.select(selector)
                if team_rows:
                    break

            # Search for team
            for row in team_rows:
                team_element = row.select_one('.name, .team-name, [class*="name"]')
                if team_element:
                    found_team = clean_text(team_element.get_text())
                    if normalize_team_name(found_team) == normalize_team_name(team_name):
                        # Extract ranking
                        rank_element = row.select_one('.position, .rank, [class*="position"]')
                        ranking = parse_ranking(rank_element.get_text()) if rank_element else 0

                        # Extract points
                        points_element = row.select_one('.points, [class*="points"]')
                        points = 0
                        if points_element:
                            try:
                                points = int(clean_text(points_element.get_text()))
                            except:
                                points = 0

                        team_data = Team(
                            name=found_team,
                            ranking=ranking,
                            elo_rating=points
                        )

                        response_time = (datetime.now() - start_time).total_seconds()

                        logger.info(f"✅ Found {found_team} at rank #{ranking}")

                        return ScrapingResult(
                            success=True,
                            data=team_data,
                            source_url=URL_CONFIG.HLTV_RANKINGS,
                            response_time=response_time
                        )

            # Team not found
            logger.warning(f"⚠️ Team '{team_name}' not found in rankings")
            return ScrapingResult(
                success=False,
                error_message=f"Team '{team_name}' not found in rankings",
                source_url=URL_CONFIG.HLTV_RANKINGS,
                response_time=(datetime.now() - start_time).total_seconds()
            )

        except Exception as e:
            logger.error(f"❌ Error scraping team ranking: {e}")
            return ScrapingResult(
                success=False,
                error_message=str(e),
                source_url=URL_CONFIG.HLTV_RANKINGS,
                response_time=(datetime.now() - start_time).total_seconds()
            )
